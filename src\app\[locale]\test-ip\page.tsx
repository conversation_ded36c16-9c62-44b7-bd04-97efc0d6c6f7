'use client';

import React, { useState } from 'react';
import { useLocale } from 'next-intl';

export default function TestIPPage() {
  const [testResult, setTestResult] = useState<string>('');
  const currentLocale = useLocale();

  // 测试中国IP重定向
  const testChinaIP = () => {
    // 在新窗口中打开，模拟中国IP
    const testUrl = `${window.location.origin}/?test_china=true`;
    window.open(testUrl, '_blank');
  };

  // 测试外国IP重定向
  const testForeignIP = () => {
    // 在新窗口中打开，模拟外国IP
    const testUrl = `${window.location.origin}/?test_china=false`;
    window.open(testUrl, '_blank');
  };

  // 使用curl命令测试
  const getCurlCommands = () => {
    const baseUrl = window.location.origin;
    return {
      china: `curl -I -H "X-Forwarded-For: *******" ${baseUrl}/`,
      foreign: `curl -I -H "X-Forwarded-For: *******" ${baseUrl}/`,
      local: `curl -I ${baseUrl}/`
    };
  };

  const curlCommands = getCurlCommands();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold mb-6">IP检测本地测试工具</h1>
          
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold mb-3">当前状态</h2>
            <p><strong>当前语言:</strong> <code className="bg-blue-100 px-2 py-1 rounded">{currentLocale}</code></p>
            <p><strong>当前URL:</strong> <code className="bg-gray-100 px-2 py-1 rounded text-sm">{typeof window !== 'undefined' ? window.location.href : ''}</code></p>
          </div>

          <div className="space-y-6">
            {/* 方法1：环境变量测试 */}
            <div className="p-4 border rounded-lg">
              <h3 className="text-lg font-semibold mb-3">方法1：环境变量测试</h3>
              <p className="text-sm text-gray-600 mb-3">
                通过设置环境变量来模拟中国IP测试
              </p>
              
              <div className="bg-gray-100 p-3 rounded-md mb-3">
                <p className="text-sm font-mono">
                  # 测试中国IP重定向<br/>
                  TEST_CHINA_IP=true npm run dev
                </p>
              </div>
              
              <div className="bg-gray-100 p-3 rounded-md">
                <p className="text-sm font-mono">
                  # 正常模式（非中国IP）<br/>
                  npm run dev
                </p>
              </div>
            </div>

            {/* 方法2：curl命令测试 */}
            <div className="p-4 border rounded-lg">
              <h3 className="text-lg font-semibold mb-3">方法2：curl命令测试</h3>
              <p className="text-sm text-gray-600 mb-3">
                使用curl命令模拟不同IP地址的请求
              </p>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium mb-1">测试中国IP (*******):</p>
                  <div className="bg-gray-100 p-2 rounded-md">
                    <code className="text-xs">{curlCommands.china}</code>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium mb-1">测试外国IP (*******):</p>
                  <div className="bg-gray-100 p-2 rounded-md">
                    <code className="text-xs">{curlCommands.foreign}</code>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium mb-1">测试本地IP:</p>
                  <div className="bg-gray-100 p-2 rounded-md">
                    <code className="text-xs">{curlCommands.local}</code>
                  </div>
                </div>
              </div>
            </div>

            {/* 方法3：浏览器开发者工具 */}
            <div className="p-4 border rounded-lg">
              <h3 className="text-lg font-semibold mb-3">方法3：浏览器开发者工具</h3>
              <p className="text-sm text-gray-600 mb-3">
                使用浏览器开发者工具修改请求头
              </p>
              
              <ol className="text-sm space-y-2">
                <li>1. 打开浏览器开发者工具 (F12)</li>
                <li>2. 进入 Network 标签页</li>
                <li>3. 右键点击任意请求 → Edit and Resend</li>
                <li>4. 添加请求头：<code className="bg-gray-100 px-1">X-Forwarded-For: *******</code></li>
                <li>5. 发送请求查看重定向结果</li>
              </ol>
            </div>

            {/* 方法4：代理工具 */}
            <div className="p-4 border rounded-lg">
              <h3 className="text-lg font-semibold mb-3">方法4：使用代理工具</h3>
              <p className="text-sm text-gray-600 mb-3">
                使用Postman、Insomnia等工具测试
              </p>
              
              <div className="text-sm space-y-2">
                <p><strong>Postman测试步骤：</strong></p>
                <ol className="ml-4 space-y-1">
                  <li>1. 创建GET请求到 <code className="bg-gray-100 px-1">{typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3684'}/</code></li>
                  <li>2. 在Headers中添加：<code className="bg-gray-100 px-1">X-Forwarded-For: *******</code></li>
                  <li>3. 发送请求，查看响应头中的重定向信息</li>
                </ol>
              </div>
            </div>

            {/* 预期结果 */}
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-green-800">预期测试结果</h3>
              <div className="text-sm space-y-2 text-green-700">
                <p><strong>中国IP (如*******):</strong> 重定向到 <code>/zh-Hans</code></p>
                <p><strong>外国IP (如*******):</strong> 重定向到 <code>/en</code> (默认)</p>
                <p><strong>本地IP:</strong> 重定向到 <code>/en</code> (除非设置TEST_CHINA_IP=true)</p>
              </div>
            </div>
          </div>

          {/* 快速测试按钮 */}
          <div className="mt-6 pt-6 border-t">
            <h3 className="text-lg font-semibold mb-3">快速测试</h3>
            <div className="flex gap-3">
              <a
                href="/"
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              >
                测试根路径重定向
              </a>
              <a
                href="/zh-Hans"
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              >
                直接访问中文版
              </a>
              <a
                href="/en"
                className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
              >
                直接访问英文版
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

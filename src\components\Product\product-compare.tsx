"use client"
import { useTranslations } from "next-intl"
import { useEffect, useState } from "react"
import { getCollectionProducts } from "@/lib/api/product"
import { ProductListItemFragment } from "@/gql/graphql"
import {Link} from "@/navigation"
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage"
import { handlerInnerHtml } from "../BodyText";
import { filterSortDsc } from "./product-card";
import { defaultLocale } from "@/config";
import { motion } from "framer-motion";
import { translateStaticProps } from "@/lib/utils/translate";
import { MinusOutlined } from '@ant-design/icons';
// 骨架屏组件
const ProductSkeleton = () => (
    <div className="flex flex-col items-center text-center">
        {/* 产品图片骨架 */}
        <div className="relative w-full aspect-square overflow-hidden mb-6 bg-gray-200 rounded-lg" />

        {/* 产品信息骨架 */}
        <div className="w-full space-y-4">
            {/* 产品名称骨架 */}
            <div className="h-8 bg-gray-200 rounded-lg w-3/4 mx-auto" />

            {/* 产品描述骨架 */}
            <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-full" />
                <div className="h-4 bg-gray-200 rounded w-2/3 mx-auto" />
            </div>

            {/* 价格骨架 */}
            <div className="h-6 bg-gray-200 rounded w-1/2 mx-auto" />

            {/* 按钮骨架 */}
            <div className="space-y-3 flex items-center justify-center gap-4">
                <div className="h-12 bg-gray-200 rounded-full w-32" />
                <div className="h-4 bg-gray-200 rounded w-20" />
            </div>

            {/* 特性图标骨架 */}
            <div className="pt-6">
                <div className="flex flex-col gap-4">
                    {[...Array(4)].map((_, i) => (
                        <div key={i} className="flex flex-col items-center">
                            <div className="w-20 h-20 bg-gray-200 rounded-lg mb-2" />
                            <div className="h-3 bg-gray-200 rounded w-16" />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    </div>
);

interface ProductCompareProps {
    categorySlug: string
    locale: string
    channel: string
}

// Motion 动画配置
const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
        }
    }
};

const itemVariants = {
    hidden: {
        opacity: 0,
        y: 20
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.6,
            ease: "easeOut"
        }
    }
};

export default function ProductCompare({ categorySlug, locale, channel }: ProductCompareProps) {
    const t = useTranslations();
    const [products, setProducts] = useState<ProductListItemFragment[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [translatedProductInfo, setTranslatedProductInfo] = useState<{[productId: string]: any[]}>({});

    useEffect(() => {
        const fetchProducts = async () => {
            try {
                setLoading(true);
                const response = await getCollectionProducts({
                    slug: "inspiration-cube-series-2",
                    locale: locale,
                    channel: channel
                });
                console.log('response', response);
                if (response?.collection?.products?.edges) {
                    const productList = response.collection.products.edges.map((edge: any) => edge.node);
                    setProducts(productList);
                    // 翻译产品特性信息
                    await translateProductInfo(productList);
                }
            } catch (error) {
                setError(error as string);
                console.error("Error fetching products:", error);
            } finally {
                setLoading(false);
            }
        };
        fetchProducts();
    }, [categorySlug, locale, channel]);

    // 解析产品媒体数据
    const parseProductMedia = (mediaString: string | null): any[] => {
        if (!mediaString) return [];
        try {
            const parsed = JSON.parse(mediaString);
            return Array.isArray(parsed) ? parsed : [];
        } catch {
            return [];
        }
    };

    // 获取产品主图
    const getProductImage = (product: ProductListItemFragment) => {
        const media = parseProductMedia(product.media);
        if (media.length > 0) {
            return media[0].url;
        }
        return product.thumbnail?.url || '/placeholder-product.jpg';
    };

    // 获取产品价格
    const getProductPrice = (product: ProductListItemFragment) => {
        const price = product.pricing?.priceRange?.start?.gross;
        if (price) {
            return `${price.currency} ${price.amount}`;
        }
        return t('product.priceOnRequest');
    };

    // 获取产品名称
    const getProductName = (product: ProductListItemFragment) => {
        return product.translation?.name || product.name;
    };

    // 解析产品特性信息（原始数据）
    const getProductInfoDetailRaw = (product: ProductListItemFragment) => {
        if (!product.metadata || !Array.isArray(product.metadata)) return [];

        const infoDetailMeta = product.metadata.find((meta: any) => meta.key === 'infoDetail');
        if (!infoDetailMeta || !infoDetailMeta.value) return [];

        try {
            const infoDetail = JSON.parse(infoDetailMeta.value) as any;
            // 返回 productInfo 数组，如果不存在则返回空数组
            return infoDetail?.productInfo || [];
        } catch (error) {
            console.error('Error parsing infoDetail:', error);
            return [];
        }
    };

    // 获取翻译后的产品特性信息
    const getProductInfoDetail = (product: ProductListItemFragment) => {
        // 如果有翻译后的数据，优先使用翻译后的数据
        if (translatedProductInfo[product.id]) {
            return translatedProductInfo[product.id];
        }
        // 否则返回原始数据
        return getProductInfoDetailRaw(product);
    };

    // 翻译产品特性信息
    const translateProductInfo = async (products: ProductListItemFragment[]) => {
        if (locale === defaultLocale) return;

        const translatedData: {[productId: string]: any[]} = {};

        for (const product of products) {
            const rawInfo = getProductInfoDetailRaw(product);
            if (rawInfo.length > 0) {
                try {
                    // 翻译标题和描述
                    const translatedInfo = await translateStaticProps(
                        rawInfo,
                        ['title', 'description'],
                        'auto',
                        locale
                    );
                    translatedData[product.id] = translatedInfo;
                } catch (error) {
                    console.error('Error translating product info:', error);
                    translatedData[product.id] = rawInfo; // 翻译失败时使用原始数据
                }
            }
        }

        setTranslatedProductInfo(translatedData);
    };

    if (error) {
        return (
            <div className="text-center py-20">
                <p className="text-red-500">{t('common.error')}: {error}</p>
            </div>
        );
    }

    return (
        <div className="w-full bg-[#f7f7f7] py-12 md:py-16 lg:py-20">
            <div className="container">
                {/* 标题区域 */}
                <motion.div
                    className="text-center mb-12 lg:mb-16 flex justify-between items-center flex-wrap"
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                >
                    <h2 className="text-2xl  font-bold text-black mb-4">
                        {t('common.allSeriesProducts')}
                    </h2>
                    <div className="flex items-center">
                        <Link
                            href="/compare"
                            className="text-mainColor hover:text-opacity-80 text-base lg:text-lg  transition-colors duration-200"
                        >
                            {t('common.viewAllProducts')} &nbsp; &gt;
                        </Link>
                    </div>
                </motion.div>

                {/* 产品网格 */}
                <motion.div
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-16 lg:gap-8"
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                >
                    {loading ? (
                        // 显示骨架屏
                        [...Array(4)].map((_, index) => (
                            <motion.div key={index} variants={itemVariants}>
                                <ProductSkeleton />
                            </motion.div>
                        ))
                    ) : (
                        // 显示实际产品
                        products.slice(0, 4).map((product, index) => (
                            <motion.div
                                key={product.id}
                                variants={itemVariants}
                                className="flex flex-col items-center text-center transition-all duration-300 ease-out transform hover:-translate-y-2 hover:scale-[1.02]"
                            >
                                {/* 产品图片 */}
                                <div className="relative w-full aspect-square overflow-hidden mb-6 transition-transform duration-300">
                                    <SEOOptimizedImage
                                        src={getProductImage(product)}
                                        alt={getProductName(product)}
                                        width={1000}
                                        height={1000}
                                        quality={100}
                                        className="w-full h-full object-contain transition-transform duration-300"
                                    />
                                </div>

                                {/* 产品信息 */}
                                <div className="w-full">
                                    {/* 产品名称 */}
                                    <h3 className="text-[20px] text-[#1d1d1f] mb-[30px] line-clamp-1 leading-tight font-semibold">
                                        {getProductName(product)}
                                    </h3>

                                    {/* 产品描述/标语 */}
                                    <div className="text-[12px] text-[#1d1d1f] mb-4 min-h-[40px] line-clamp-2 leading-relaxed"
                                        dangerouslySetInnerHTML={{
                                            __html: handlerInnerHtml(locale == defaultLocale
                                                ? filterSortDsc(product?.descriptionJson).sortDsc
                                                : filterSortDsc(product?.translation?.descriptionJson).sortDsc ||
                                                filterSortDsc(product?.descriptionJson).sortDsc)
                                        }}
                                    ></div>

                                    {/* 价格信息 */}
                                    <div className="text-[17px] font-semibold text-gray-900 mb-[30px]">
                                        {getProductPrice(product)}
                                    </div>

                                    {/* 操作按钮 */}
                                    <div className="flex  justify-center gap-8 items-center mb-[38px]">
                                        <Link href={`/product/${product.slug}`} className="bg-mainColor hover:bg-opacity-80 text-white hover:text-white font-medium px-8 py-3 rounded-full transition-all duration-200 hover:shadow-lg transform">
                                            {t('common.Learn_more')}
                                        </Link>
                                        <Link
                                            href={`/product/${product.slug}`}
                                            className="text-mainColor hover:text-opacity-80  font-medium transition-colors duration-200 hover:underline"
                                        >
                                            {t('common.buy')} &nbsp; &gt;
                                        </Link>
                                    </div>

                                    {/* 产品特性图标 - 动态渲染 */}
                                    <div className="pt-[38px] border-t border-gray-200">
                                        <div className="flex flex-col gap-8">
                                            {getProductInfoDetail(product).map((info: any, infoIndex: number) => (
                                                <div key={info.id || infoIndex} className="flex flex-col items-center text-center">
                                                    {/* 特性图标 */}
                                                    <div className="w-[56px] h-[56px] flex items-center justify-center transition-colors duration-200">
                                                        {info.media && info.media.length > 0 ? (
                                                            // 如果有媒体文件，显示图片
                                                            <SEOOptimizedImage
                                                                src={info.media[0].url}
                                                                alt={info.media[0].alt || info.title}
                                                                width={1000}
                                                                height={1000}
                                                                className="w-full h-full object-contain"
                                                            />
                                                        ) : (
                                                            // 如果没有媒体文件，显示默认图标
                                                            // <svg className="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                                            //     <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v8a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 1v6h10V5H5z" clipRule="evenodd" />
                                                            // </svg>
                                                            <div className="text-black text-2xl">
                                                                <MinusOutlined />
                                                            </div>
                                                        )}
                                                    </div>
                                                    {/* 特性标题 */}
                                                    <p className="text-[14px] text-[#1d1d1f] mt-4 mb-2  text-center leading-relaxed">
                                                        {info?.title || ''}
                                                    </p>
                                                    {/* 特性描述（可选显示） */}
                                                    {info.description && (
                                                        <p className="text-[12px] text-[#1d1d1f]  text-center  leading-loose">
                                                            {info?.description || ''}
                                                        </p>
                                                    )}
                                                </div>
                                            ))}

                                            {/* 如果没有特性信息，显示默认内容 */}
                                            {/* {getProductInfoDetail(product).length === 0 && (
                                            <div className="flex flex-col items-center text-center">
                                                <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                                                    <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v8a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 1v6h10V5H5z" clipRule="evenodd" />
                                                    </svg>
                                                </div>
                                                <span className="text-xs text-gray-400 font-medium">{t('product.noFeatures')}</span>
                                            </div>
                                        )} */}
                                        </div>
                                    </div>
                                </div>
                            </motion.div>
                        ))
                    )}
                </motion.div>
            </div>
        </div>
    )
}
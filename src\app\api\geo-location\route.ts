import { NextRequest, NextResponse } from 'next/server';
import { detectUserLocale } from '@/lib/utils/ipDetection';

/**
 * 获取客户端真实IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  const xClientIP = request.headers.get('x-client-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) return realIP;
  if (cfConnectingIP) return cfConnectingIP;
  if (xClientIP) return xClientIP;

  return request.ip || '127.0.0.1';
}

/**
 * 使用第三方服务检测IP地理位置
 */
async function detectLocationByIP(ip: string): Promise<{ country: string; countryCode: string; isChina: boolean }> {
  // 本地IP处理
  if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
    return {
      country: 'Local',
      countryCode: 'LOCAL',
      isChina: process.env.NODE_ENV === 'development' // 开发环境默认为中国
    };
  }

  try {
    // 方案1: 使用免费的 ipapi.co 服务
    const response = await fetch(`https://ipapi.co/${ip}/json/`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; GeoLocationBot/1.0)'
      },
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (response.ok) {
      const data = await response.json();
      return {
        country: data.country_name || 'Unknown',
        countryCode: data.country_code || 'UNKNOWN',
        isChina: data.country_code === 'CN'
      };
    }
  } catch (error) {
    console.error('IP地理位置检测失败 (ipapi.co):', error);
  }

  try {
    // 方案2: 备用服务 ip-api.com
    const response = await fetch(`http://ip-api.com/json/${ip}?fields=status,country,countryCode`, {
      next: { revalidate: 3600 }
    });

    if (response.ok) {
      const data = await response.json();
      if (data.status === 'success') {
        return {
          country: data.country || 'Unknown',
          countryCode: data.countryCode || 'UNKNOWN',
          isChina: data.countryCode === 'CN'
        };
      }
    }
  } catch (error) {
    console.error('IP地理位置检测失败 (ip-api.com):', error);
  }

  // 如果所有服务都失败，使用简单的IP段判断
  return fallbackIPDetection(ip);
}

/**
 * 备用IP检测方法
 */
function fallbackIPDetection(ip: string): { country: string; countryCode: string; isChina: boolean } {
  const ipParts = ip.split('.').map(Number);
  if (ipParts.length !== 4) {
    return { country: 'Unknown', countryCode: 'UNKNOWN', isChina: false };
  }

  const firstOctet = ipParts[0];
  // 中国主要运营商的IP段首字节
  const chineseFirstOctets = [
    1, 14, 27, 36, 39, 42, 49, 58, 59, 60, 61, 101, 103, 106, 110, 111, 112, 113, 114, 115, 
    116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 180, 182, 183, 202, 203, 210, 211, 
    218, 219, 220, 221, 222, 223
  ];

  const isChina = chineseFirstOctets.includes(firstOctet);
  
  return {
    country: isChina ? 'China' : 'Unknown',
    countryCode: isChina ? 'CN' : 'UNKNOWN',
    isChina
  };
}

/**
 * GET /api/geo-location
 * 获取用户地理位置信息
 */
export async function GET(request: NextRequest) {
  try {
    const clientIP = getClientIP(request);
    const locationData = await detectUserLocale(clientIP);

    return NextResponse.json({
      success: true,
      data: locationData
    });
  } catch (error) {
    console.error('地理位置检测API错误:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to detect location',
      data: {
        ip: 'unknown',
        country: 'Unknown',
        countryCode: 'UNKNOWN',
        isChina: false,
        recommendedLocale: 'en',
        source: 'error',
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}

/**
 * POST /api/geo-location
 * 手动设置用户语言偏好
 */
export async function POST(request: NextRequest) {
  try {
    const { locale } = await request.json();
    
    if (!locale || !['en', 'zh-Hans'].includes(locale)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid locale'
      }, { status: 400 });
    }
    
    const response = NextResponse.json({
      success: true,
      message: 'Language preference updated',
      locale
    });
    
    // 设置语言偏好cookie
    response.cookies.set('NEXT_LOCALE', locale, {
      maxAge: 60 * 60 * 24 * 365, // 1年
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });
    
    return response;
  } catch (error) {
    console.error('设置语言偏好错误:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to set language preference'
    }, { status: 500 });
  }
}

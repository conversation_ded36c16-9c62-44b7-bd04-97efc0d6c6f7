"use client"
import { useTranslations } from "next-intl"
import { Link } from "@/navigation"
import { motion } from "framer-motion";
import { useState, useRef, useEffect } from "react";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";

// 动画变体 - 淡入入场动画
const containerVariants = {
    hidden: {},
    visible: {
        transition: {
            staggerChildren: 0.15,
            delayChildren: 0.1
        }
    }
};

const fadeInVariants = {
    hidden: {
        opacity: 0,
        y: 30
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.8,
            ease: [0.25, 0.46, 0.45, 0.94]
        }
    }
};

const titleVariants = {
    hidden: {
        opacity: 0,
        y: 40
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 1,
            ease: [0.25, 0.46, 0.45, 0.94]
        }
    }
};

const textVariants = {
    hidden: {
        opacity: 0,
        y: 25
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.7,
            ease: "easeOut"
        }
    }
};

const buttonVariants = {
    hidden: {
        opacity: 0,
        y: 20
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.6,
            ease: "easeOut"
        }
    }
};

export default function AboutUs() {
    const t = useTranslations()
    const videoRef = useRef<HTMLVideoElement>(null);
    const [isPlaying, setIsPlaying] = useState(false);

    const handleVideoPlayPause = () => {
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.pause();
            } else {
                videoRef.current.play();
            }
            setIsPlaying(!isPlaying);
        }
    };


    return (
        <motion.section
            className="bg-[#f5f5f7] overflow-hidden"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
        >
            <motion.h1
                className="text-4xl font-bold uppercase text-center py-10"
                variants={titleVariants}
            >
                {t("about.aboutTitle")}
            </motion.h1>

            <motion.div
                className="container text-[#999] flex flex-col gap-3"
                variants={textVariants}
            >
                <p className="text-center">
                    {t("about.ap1")}
                </p>
                <p className="text-center">
                    {t("about.ap2")}
                </p>
                <p className="text-center">
                    {t("about.ap3")}
                </p>
                <p className="text-center">
                    {t("about.ap4")}
                </p>
                <p className="text-center">
                    {t("about.ap5")}
                </p>
            </motion.div>
            <motion.div
                className="flex justify-center py-10"
                variants={buttonVariants}
            >
                <Link className="inline-block text-lg bg-black text-white hover:text-white hover:bg-mainColor duration-300 px-20 font-semibold py-2 rounded-full" href="/about-us">
                    {t("base.viewMore")}
                </Link>
            </motion.div>
            {/* 视频区域 - 不使用动画 */}
            <div className="container">
                <div className="relative w-full aspect-video overflow-hidden">
                    {/* 自定义播放按钮层 - 仅在视频暂停时显示 */}
                    {!isPlaying && (
                        <div
                            className="absolute inset-0 flex items-center justify-center transition-all duration-500 z-10 cursor-pointer"
                            onClick={handleVideoPlayPause}
                        >
                            <div className="w-20 h-20 flex items-center justify-center rounded-full bg-mainColor shadow-lg hover:scale-110 transition-transform duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-white" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M8 5v14l11-7z" />
                                </svg>
                            </div>
                        </div>
                    )}

                    <video
                        ref={videoRef}
                        className="w-full aspect-video object-cover"
                        poster="/image/about/company.png"
                        preload="metadata"
                        playsInline
                        controls
                        onPlay={() => setIsPlaying(true)}
                        onPause={() => setIsPlaying(false)}
                        onEnded={() => setIsPlaying(false)}
                    >
                        <source src="/video/factory2.webm" type="video/webm" />
                        <source src="/video/factory2.mp4" type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </motion.section>
    )
}
"use client";
import { TLocale } from "@/lib/@types/locale";
import { SITE_LOCALES } from "@/lib/constant";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { usePathname } from "next/navigation";
import { useLocale } from "next-intl";
import { defaultLocale } from "@/config";
import { Link } from "@/navigation";
import FlagIcon from "@/components/Flag";
import * as Icon from "@phosphor-icons/react/dist/ssr";
import { Modal, Button, Row, Col, Typography, Divider } from "antd";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;

// 自定义样式
const modalStyles = `
  .language-modal {
    border-radius: 0 !important;
  }
  .language-modal .ant-modal-content {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.06);
  }

  .language-modal .ant-modal-header {
    border-bottom: none;
    padding: 0;
  }

  .language-modal .ant-modal-close {
    top: 20px;
    right: 20px;
    color: #6b7280;
    font-size: 18px;
  }

  .language-modal .ant-modal-close:hover {
    color: #374151;
    background-color: rgba(0, 0, 0, 0.04);
  }
`;
// 地区分组配置
const regionGroups = {
  "common.AsiaPacific": ["zh-Hans", "zh-Hant", "ja", "ko", "id", "th", "vi", "ms"],
  "common.Europe": ["en", "es", "fr", "ru"],
  "common.MiddleEast": ["ar"]
};

// 动画变体
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

const Translate = ({ lastScrollPosition }: { lastScrollPosition?: number }) => {
  const currentLocales: TLocale[] = SITE_LOCALES;
  const [isModalVisible, setIsModalVisible] = useState(false);
  const pathname = usePathname();
  const locale = useLocale();
  const [currentSlug, setCurrentSlug] = useState("");
  const t = useTranslations();
  const getCurrentLocale = useCallback(() => {
    return currentLocales.find((item) => item.code === locale);
  }, [currentLocales, locale]);

  const showModal = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  const handleCancel = useCallback(() => {
    setIsModalVisible(false);
  }, []);

  useEffect(() => {
    if (pathname) {
      setCurrentSlug(locale === defaultLocale ? pathname : pathname.replace(locale, ""));
    }
  }, [pathname, locale]);

  // 根据地区分组语言
  const getLocalesByRegion = (region: string) => {
    const regionCodes = regionGroups[region as keyof typeof regionGroups] || [];
    return currentLocales.filter(locale => regionCodes.includes(locale.code));
  };

  if (process.env.NEXT_PUBLIC_IS_I18N !== "true") {
    return null;
  }

  return (
    <>
      <style jsx global>{modalStyles}</style>
      <div className="c-flex cursor-pointer px-2">
        <button
          onClick={showModal}
          className="flex items-center gap-2 rounded-lg py-2 text-sm font-medium transition-colors text-[#282828] hover:text-mainColor"
          aria-label="Select Language"
        >
          <Icon.Globe size={22} />
          {/* <FlagIcon flag={getCurrentLocale()?.flag || ""} /> */}
          <span className="hidden md:inline">{getCurrentLocale()?.nativeName}</span>
        </button>
      </div>

      <Modal
        title={null}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={900}
        centered
        className="language-modal"
        styles={{
          body: { padding: 0 },
          content: { borderRadius: '0', overflow: 'hidden' }
        }}
      >
        <div className="px-8 py-6">
          <div className="text-center mb-8">
            <Title level={4} className="!pb-3 !text-[#6c7073] !text-[16px] !font-normal border-b">
              {t("common.SelectYourCountry")}
            </Title>
            {/* <Text className="text-gray-500 text-base">
              Choose your preferred language and region
            </Text> */}
          </div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {Object.entries(regionGroups).map(([region, codes]) => {
              const regionLocales = getLocalesByRegion(region);
              if (regionLocales.length === 0) return null;

              return (
                <motion.div key={region} variants={itemVariants} className="mb-8">
                  <Title level={4} className="!mb-5 !text-[#6c7073] !font-normal !text-[16px] !sans">
                    {t(region)}
                  </Title>
                  <Row gutter={[12, 12]}>
                    {regionLocales.map((item) => (
                      <Col key={item.code} xs={24} sm={12} md={8}>
                        <Link
                          href={currentSlug}
                          locale={item.code}
                          onClick={handleCancel}
                          className="block"
                        >
                          {/* ${locale === item.code
                                ? 'border-blue-500'
                                : 'bg-[#f7f9fa]'
                              } */}
                          <motion.div
                            className={`
                              relative px-5 py-2 transition-all duration-300 cursor-pointer bg-[#f7f9fa]
                            `}
                          // whileHover={{ scale: 1.02 }}
                          // whileTap={{ scale: 0.98 }}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3 w-full">
                                {/* <FlagIcon flag={item.flag} /> */}
                                <div className="flex justify-between w-full items-center gap-2">
                                  <div className="text-[#383e40] text-[12px] " style={{ fontWeight: "normal" }}>
                                    {item.name}
                                  </div>
                                  <div className="text-[12px] text-[#9fa3a6] " style={{ fontWeight: "normal" }}>
                                    {item.nativeName}
                                  </div>
                                </div>
                              </div>
                              {/* {locale === item.code && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
                                >
                                  <Icon.Check size={16} className="text-white" />
                                </motion.div>
                              )} */}
                            </div>
                          </motion.div>
                        </Link>
                      </Col>
                    ))}
                  </Row>
                  {/* {region !== "Middle East" && <Divider className="!my-6" />} */}
                </motion.div>
              );
            })}
          </motion.div>
        </div>
      </Modal>
    </>
  );
};

export default Translate;

import createMiddleware from "next-intl/middleware";
import { defaultLocale, localePrefix, locales, pathnames } from "./config";
import { NextRequest, NextResponse } from "next/server";

/**
 * 获取用户IP地址
 */
function getClientIP(request: NextRequest): string {
	const forwarded = request.headers.get('x-forwarded-for');
	const realIP = request.headers.get('x-real-ip');
	const cfConnectingIP = request.headers.get('cf-connecting-ip');

	if (forwarded) {
		return forwarded.split(',')[0].trim();
	}

	if (realIP) return realIP;
	if (cfConnectingIP) return cfConnectingIP;

	return request.ip || '127.0.0.1';
}

/**
 * 检查IP是否为中国IP
 */
function isChineseIP(ip: string): boolean {
	// 本地开发环境跳过检测
	// if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
	// 	return false;
	// }

	const ipParts = ip.split('.').map(Number);
	if (ipParts.length !== 4) return false;

	// 中国主要运营商的IP段首字节
	const chineseFirstOctets = [
		1, 14, 27, 36, 39, 42, 49, 58, 59, 60, 61, 101, 103, 106, 110, 111, 112, 113, 114, 115,
		116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 180, 182, 183, 202, 203, 210, 211,
		218, 219, 220, 221, 222, 223
	];

	return chineseFirstOctets.includes(ipParts[0]);
}

// 定义一个默认导出的中间件函数，用于处理权限验证和路由跳转
export default function middleware(request: NextRequest) {
	const { pathname } = request.nextUrl;

	// 只处理根路径的访问
	if (pathname === '/') {
		const clientIP = getClientIP(request);

		// 只有检测到中国IP时才重定向到中文
		if (isChineseIP(clientIP)) {
			const url = request.nextUrl.clone();
			url.pathname = '/zh-Hans';
			return NextResponse.redirect(url);
		}

		// 非中国IP不做处理，让next-intl处理默认重定向
	}

	// 创建下一个中间件实例 多语言中间件
	const next = createMiddleware({
		locales,
		defaultLocale: defaultLocale,
		pathnames: pathnames,
		localePrefix: localePrefix,
		localeDetection: false
	});

	// 其他情况，继续执行下一个中间件
	return next(request);
}


export const config = {
	matcher: [
		// 在根目录中启用重定向到匹配的区域设置
"/",
		// 设置cookie以记住以前的区域设置
		// 所有具有区域设置前缀的请求
		// `/(${locales.map(item => item).join("|")})/:path*`,
		"/(af|am|ar|as|az|ba|bg|bho|bn|bo|brx|bs|ca|cs|cy|da|de|doi|dsb|dv|el|en|es|et|eu|fa|fi|fil|fj|fo|fr|fr-CA|ga|gl|gom|gu|ha|he|hi|hne|hr|hsb|ht|hu|hy|id|ig|ikt|is|it|iu|iu-Latn|ja|ka|kk|km|kmr|kn|ko|ks|ku|ky|ln|lo|lt|lug|lv|lzh|mai|mg|mi|mk|ml|mn-Cyrl|mn-Mong|mni|mr|ms|mt|mww|my|nb|ne|nl|nso|nya|or|otq|pa|pl|prs|ps|pt|pt-PT|ro|ru|run|rw|sd|si|sk|sl|sm|sn|so|sq|sr-Cyrl|sr-Latn|st|sv|sw|ta|te|th|ti|tk|tlh-Latn|tlh-Piqd|tn|to|tr|tt|ty|ug|uk|ur|uz|vi|xh|yo|yua|yue|zh-Hans|zh-Hant|zu)/:path*",
		// 启用添加缺失区域设置的重定向
		//'(e.g. `/pathnames` -> `/en/pathnames`)',
		'/((?!api|_next|_vercel|.*\\..*).*)',
	],
};
